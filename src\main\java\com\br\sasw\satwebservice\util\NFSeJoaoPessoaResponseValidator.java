package com.br.sasw.satwebservice.util;

import com.br.sasw.satwebservice.exception.BusinessException;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.StringReader;
import java.util.ArrayList;
import java.util.List;

/**
 * Utilitário para validar respostas SOAP do serviço de NFSe de João Pessoa
 * e extrair mensagens de erro quando presentes.
 */
@UtilityClass
@Slf4j
public class NFSeJoaoPessoaResponseValidator {

    /**
     * Valida a resposta do serviço GerarNfse de João Pessoa.
     * Lança BusinessException se houver mensagens de erro na resposta.
     * 
     * @param soapResponse Resposta SOAP completa do serviço
     * @throws BusinessException se houver erros de validação ou outros erros
     */
    public void validateGerarNfseResponse(String soapResponse) {
        if (soapResponse == null || soapResponse.trim().isEmpty()) {
            throw new BusinessException("Resposta vazia recebida do serviço de NFSe de João Pessoa");
        }

        try {
            Document doc = parseXmlResponse(soapResponse);
            
            // Busca por mensagens de retorno na resposta
            List<MensagemRetorno> mensagens = extractMensagensRetorno(doc);
            
            if (!mensagens.isEmpty()) {
                // Se há mensagens de retorno, significa que houve algum problema
                String mensagemCompleta = buildErrorMessage(mensagens);
                log.warn("Erro na resposta do serviço NFSe João Pessoa: {}", mensagemCompleta);
                throw new BusinessException(mensagemCompleta);
            }
            
            // Se chegou até aqui, a resposta não contém erros explícitos
            log.debug("Resposta do serviço NFSe João Pessoa validada com sucesso");
            
        } catch (BusinessException e) {
            // Re-lança BusinessException
            throw e;
        } catch (Exception e) {
            log.error("Erro ao validar resposta do serviço NFSe João Pessoa", e);
            throw new BusinessException("Erro ao processar resposta do serviço de NFSe: " + e.getMessage());
        }
    }

    /**
     * Valida a resposta do serviço ConsultarNfsePorRps de João Pessoa.
     * 
     * @param soapResponse Resposta SOAP completa do serviço
     * @throws BusinessException se houver erros de validação ou outros erros
     */
    public void validateConsultarNfseResponse(String soapResponse) {
        if (soapResponse == null || soapResponse.trim().isEmpty()) {
            throw new BusinessException("Resposta vazia recebida do serviço de consulta NFSe de João Pessoa");
        }

        try {
            Document doc = parseXmlResponse(soapResponse);
            
            // Busca por mensagens de retorno na resposta
            List<MensagemRetorno> mensagens = extractMensagensRetorno(doc);
            
            if (!mensagens.isEmpty()) {
                String mensagemCompleta = buildErrorMessage(mensagens);
                log.warn("Erro na consulta NFSe João Pessoa: {}", mensagemCompleta);
                throw new BusinessException(mensagemCompleta);
            }
            
            log.debug("Resposta da consulta NFSe João Pessoa validada com sucesso");
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("Erro ao validar resposta da consulta NFSe João Pessoa", e);
            throw new BusinessException("Erro ao processar resposta da consulta NFSe: " + e.getMessage());
        }
    }

    /**
     * Faz o parsing da resposta XML SOAP
     */
    private Document parseXmlResponse(String xmlResponse) throws Exception {
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        factory.setNamespaceAware(true);
        DocumentBuilder builder = factory.newDocumentBuilder();
        return builder.parse(new InputSource(new StringReader(xmlResponse)));
    }

    /**
     * Extrai todas as mensagens de retorno do documento XML
     */
    private List<MensagemRetorno> extractMensagensRetorno(Document doc) {
        List<MensagemRetorno> mensagens = new ArrayList<>();
        
        // Busca por elementos MensagemRetorno em qualquer namespace
        NodeList mensagemNodes = doc.getElementsByTagName("MensagemRetorno");
        
        for (int i = 0; i < mensagemNodes.getLength(); i++) {
            Element mensagemElement = (Element) mensagemNodes.item(i);
            MensagemRetorno mensagem = extractMensagemRetorno(mensagemElement);
            if (mensagem != null) {
                mensagens.add(mensagem);
            }
        }
        
        return mensagens;
    }

    /**
     * Extrai uma mensagem de retorno de um elemento XML
     */
    private MensagemRetorno extractMensagemRetorno(Element mensagemElement) {
        try {
            String codigo = getElementText(mensagemElement, "Codigo");
            String mensagem = getElementText(mensagemElement, "Mensagem");
            String correcao = getElementText(mensagemElement, "Correcao");
            
            if (codigo != null || mensagem != null) {
                return new MensagemRetorno(codigo, mensagem, correcao);
            }
            
        } catch (Exception e) {
            log.warn("Erro ao extrair mensagem de retorno: {}", e.getMessage());
        }
        
        return null;
    }

    /**
     * Obtém o texto de um elemento filho
     */
    private String getElementText(Element parent, String tagName) {
        NodeList nodes = parent.getElementsByTagName(tagName);
        if (nodes.getLength() > 0) {
            Element element = (Element) nodes.item(0);
            return element.getTextContent();
        }
        return null;
    }

    /**
     * Constrói uma mensagem de erro completa a partir das mensagens de retorno
     */
    private String buildErrorMessage(List<MensagemRetorno> mensagens) {
        if (mensagens.size() == 1) {
            MensagemRetorno msg = mensagens.get(0);
            StringBuilder sb = new StringBuilder();
            
            if (msg.getCodigo() != null) {
                sb.append("Código: ").append(msg.getCodigo()).append(" - ");
            }
            
            if (msg.getMensagem() != null) {
                sb.append(msg.getMensagem());
            }
            
            if (msg.getCorrecao() != null && !msg.getCorrecao().trim().isEmpty()) {
                sb.append(" (").append(msg.getCorrecao()).append(")");
            }
            
            return sb.toString();
        } else {
            StringBuilder sb = new StringBuilder("Múltiplos erros encontrados: ");
            for (int i = 0; i < mensagens.size(); i++) {
                if (i > 0) sb.append("; ");
                MensagemRetorno msg = mensagens.get(i);
                if (msg.getCodigo() != null) {
                    sb.append(msg.getCodigo()).append(": ");
                }
                if (msg.getMensagem() != null) {
                    sb.append(msg.getMensagem());
                }
            }
            return sb.toString();
        }
    }

    /**
     * Classe interna para representar uma mensagem de retorno
     */
    private static class MensagemRetorno {
        private final String codigo;
        private final String mensagem;
        private final String correcao;

        public MensagemRetorno(String codigo, String mensagem, String correcao) {
            this.codigo = codigo;
            this.mensagem = mensagem;
            this.correcao = correcao;
        }

        public String getCodigo() {
            return codigo;
        }

        public String getMensagem() {
            return mensagem;
        }

        public String getCorrecao() {
            return correcao;
        }
    }
}
