package com.br.sasw.satwebservice.controller;

import com.br.sasw.satwebservice.service.ReinfService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@Tag(name = "Envio e Consulta REINF")
@RequestMapping("/reinf")
public class ReinfSenderController {

    private final ReinfService service;

    @PostMapping(path = "/envio")
    public String send(@RequestBody String xml, @RequestParam String id,  @RequestParam String empresa, @RequestParam String nrInscEmpregador,
                       @RequestParam String tpInscEmpregador) {

        return service.send(xml, id, empresa, nrInscEmpregador, tpInscEmpregador);
    }

    @PostMapping(path = "/consulta")
    public String get(@RequestParam String protocolo, @RequestParam String empresa) {

        return service.get(protocolo, empresa);
    }
}
