package com.br.sasw.satwebservice.controller;

import com.br.sasw.satwebservice.service.NFSEGoianiaService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@Tag(name = "Envio e Consulta NFSe Goiânia")
@RequestMapping("/nfse-goiania")
@RequiredArgsConstructor
public class NFSEGoianiaController {

    private final NFSEGoianiaService service;

    @PostMapping("/envio")
    public String send(@RequestBody String xml, @RequestParam String empresa) {
        return service.send(xml, empresa);
    }

    @PostMapping("/consulta")
    public String get(@RequestBody String xml, @RequestParam String empresa) {
        return service.get(xml, empresa);
    }
}
