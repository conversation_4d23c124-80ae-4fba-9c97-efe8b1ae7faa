package com.br.sasw.satwebservice.service;

import com.br.sasw.satwebservice.exception.BusinessException;
import com.br.sasw.satwebservice.util.NFSeJoaoPessoaXmlSigner;
import com.br.sasw.satwebservice.util.NFSeJoaoPessoaResponseValidator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.ws.transport.http.HttpComponentsMessageSender;

import java.nio.charset.StandardCharsets;

@Service
@RequiredArgsConstructor
@Slf4j
public class NFSEJoaoPessoaService {

    @Value("${nfse.joaopessoa.url}")
    private String url;

    private final HttpClientService httpClientService;

    public String send(String xml, String empresa) {
        try {

            String xmlAssinado = NFSeJoaoPessoaXmlSigner.sign(xml, empresa);

            String soapEnv = String.format("""
                <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:nfse="http://nfse.abrasf.org.br">
                   <soapenv:Header/>
                   <soapenv:Body>
                   <nfse:GerarNfse>
                      %s
                   </nfse:GerarNfse>
                   </soapenv:Body>
                </soapenv:Envelope>""", xmlAssinado);

            CloseableHttpClient httpClient = httpClientService.createHttpClientForEmpresa(empresa);
            HttpComponentsMessageSender messageSender = new HttpComponentsMessageSender();
            messageSender.setHttpClient(httpClient);

            HttpPost httpPost = new HttpPost(url);
            httpPost.setHeader("SOAPAction", "");
            httpPost.setHeader("Content-Type", "text/xml; charset=utf-8");
            
            byte[] xmlBytes = soapEnv.getBytes(StandardCharsets.UTF_8);
            int contentLength = xmlBytes.length;
            httpPost.setHeader("Content-Length", String.valueOf(contentLength));

            StringEntity entity = new StringEntity(soapEnv, "UTF-8");
            httpPost.setEntity(entity);

            CloseableHttpResponse resp = httpClient.execute(httpPost);

            String response = resp != null
                    ? EntityUtils.toString(resp.getEntity(), "UTF-8")
                    : "";

            // Valida a resposta e lança BusinessException se houver erros
            NFSeJoaoPessoaResponseValidator.validateGerarNfseResponse(response);

            return response;

        } catch(BusinessException e) {
            // Re-lança BusinessException para ser tratada pelo ExceptionHandler
            throw e;
        } catch(Exception e) {
            log.error("Erro ao enviar NFSe em João Pessoa", e);
            throw new BusinessException("Erro ao enviar NFSe para João Pessoa: " + e.getMessage());
        }
    }

    public String get(String xml, String empresa) {
        try {

            String xmlAssinado = NFSeJoaoPessoaXmlSigner.signConsulta(xml, empresa);

            String soapEnv = String.format("""
                <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:nfse="http://nfse.abrasf.org.br" xmlns:xd="http://www.w3.org/2000/09/xmldsig#">
                   <soapenv:Header/>
                   <soapenv:Body>
                      <nfse:ConsultarNfsePorRps>
                         %s
                      </nfse:ConsultarNfsePorRps>
                   </soapenv:Body>
                </soapenv:Envelope>""", xmlAssinado);

            CloseableHttpClient httpClient = httpClientService.createHttpClientForEmpresa(empresa);
            HttpComponentsMessageSender messageSender = new HttpComponentsMessageSender();
            messageSender.setHttpClient(httpClient);

            HttpPost httpPost = new HttpPost(url);
            httpPost.setHeader("SOAPAction", "");
            httpPost.setHeader("Content-Type", "text/xml; charset=utf-8");
            
            byte[] xmlBytes = soapEnv.getBytes(StandardCharsets.UTF_8);
            int contentLength = xmlBytes.length;
            httpPost.setHeader("Content-Length", String.valueOf(contentLength));

            StringEntity entity = new StringEntity(soapEnv, "UTF-8");
            httpPost.setEntity(entity);

            CloseableHttpResponse resp = httpClient.execute(httpPost);

            String response = resp != null
                    ? EntityUtils.toString(resp.getEntity(), "UTF-8")
                    : "";

            // Valida a resposta e lança BusinessException se houver erros
            NFSeJoaoPessoaResponseValidator.validateConsultarNfseResponse(response);

            return response;

        } catch(BusinessException e) {
            // Re-lança BusinessException para ser tratada pelo ExceptionHandler
            throw e;
        } catch(Exception e) {
            log.error("Erro ao consultar NFSe em João Pessoa", e);
            throw new BusinessException("Erro ao consultar NFSe em João Pessoa: " + e.getMessage());
        }
    }
}
