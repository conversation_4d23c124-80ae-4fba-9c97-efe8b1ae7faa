server.port=8081
spring.application.name=satwebservice-springboot
server.servlet.context-path=/ws/api

logging.level.org.springframework.ws.client.MessageTracing=DEBUG
logging.level.org.apache.http.wire=DEBUG
javax.net.debug=ssl

#spring.datasource.url=***************************************************************************************************
spring.datasource.url=****************************************************************************************************
spring.datasource.username=sasw
spring.datasource.password=s@$26bd1
spring.datasource.driver-class-name=com.microsoft.sqlserver.jdbc.SQLServerDriver
spring.datasource.hikari.connection-timeout=20000
logging.level.org.springframework.jdbc=DEBUG

#ESOCIAL
esocial.wsdl.context-path=com.esocial.wsdl
esocial.envio.url=https://webservices.envio.esocial.gov.br/servicos/empregador/enviarloteeventos/WsEnviarLoteEventos.svc
esocial.envio.action=http://www.esocial.gov.br/servicos/empregador/lote/eventos/envio/v1_1_0/ServicoEnviarLoteEventos/EnviarLoteEventos
esocial.consulta.url=https://webservices.consulta.esocial.gov.br/servicos/empregador/consultarloteeventos/WsConsultarLoteEventos.svc
esocial.consulta.action=http://www.esocial.gov.br/servicos/empregador/lote/eventos/envio/consulta/retornoProcessamento/v1_1_0/ServicoConsultarLoteEventos/ConsultarLoteEventos

#REINF
reinf.envio.url=https://reinf.receita.economia.gov.br/recepcao/lotes
reinf.consulta.url=https://reinf.receita.economia.gov.br/consulta/lotes/

#NFSE GOIANIA
nfse.goiania.url=https://nfse.goiania.go.gov.br/ws/nfse.asmx
nfse.goiania.envio.action=http://nfse.goiania.go.gov.br/ws/GerarNfse
nfse.goiania.consulta.action=http://nfse.goiania.go.gov.br/ws/ConsultarNfseRps

#NFSE JOAO PESSOA
nfse.joaopessoa.url=https://receita.joaopessoa.pb.gov.br/notafiscal-abrasfv203-ws/NotaFiscalSoap

# EMAIL
email.default-smtp=smtplw.com.br
email.default-username=sasw
email.default-password=xNiadJEj9607
email.default-port=587
email.default-from-email=<EMAIL>
email.default-from-name=SASw
email.scheduler-enabled=false
email.scheduler-interval-ms=30000
email.anexo-base-path=C:\\SatelliteServer\\anexo-email\\
email.log-base-path=C:\\SatelliteServer\\log\\
email.log-emails-enabled=true

# SPRING MAIL
spring.mail.host=${email.default-smtp}
spring.mail.port=${email.default-port}
spring.mail.username=${email.default-username}
spring.mail.password=${email.default-password}
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.ssl.enable=false
spring.mail.properties.mail.smtp.timeout=30000
spring.mail.properties.mail.smtp.connectiontimeout=30000

# Hibernate
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.SQLServerDialect
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.jdbc.time_zone=America/Sao_Paulo
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
spring.jpa.hibernate.naming.implicit-strategy=org.hibernate.boot.model.naming.ImplicitNamingStrategyLegacyJpaImpl
