package com.br.sasw.satwebservice.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * DTO para padronizar as respostas de erro da API
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ErrorResponse {
    
    /**
     * Timestamp do erro
     */
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS")
    private LocalDateTime timestamp;
    
    /**
     * Código de status HTTP
     */
    private int status;
    
    /**
     * Nome do erro HTTP
     */
    private String error;
    
    /**
     * Mensagem principal do erro
     */
    private String message;
    
    /**
     * Detalhes específicos do erro (opcional)
     */
    private String details;
    
    /**
     * Path da requisição que gerou o erro
     */
    private String path;
    
    /**
     * Lista de erros de validação (para casos de múltiplos erros)
     */
    private List<ValidationError> validationErrors;
    
}
